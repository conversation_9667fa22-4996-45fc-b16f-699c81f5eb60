from pyspark.sql import SparkSession, DataFrame
from pyspark.sql import functions as F
from pyspark.sql.functions import col, count, sum, aggregate, array, lit, row_number, rank, dense_rank, when, from_json, get_json_object, floor, date_format, from_utc_timestamp, round, collect_set
from pyspark.sql.window import Window
from pyspark.sql.types import StructField, BooleanType, StringType, StructType, DoubleType, LongType, TimestampType, DateType, FloatType, IntegerType, DecimalType, BinaryType, ArrayType, MapType
from pyspark.sql.utils import AnalysisException

spark = SparkSession.builder \
    .appName("Read Snappy Parquet") \
    .getOrCreate()

parquet_file_path = "/Users/<USER>/Downloads/part-00000-1222aeae-eee3-4888-b135-9b31720c3bb7-c000.snappy.parquet"
df = spark.read.option("recursiveFileLookup", "true").parquet(parquet_file_path)
# df.show(10)

# df = df.filter((col("account_id") == 1767427) & (col("asset_id") == 10005)).orderBy(col("transaction_time").asc())
df.show(truncate=False)
# df.coalesce(1).write.mode('overwrite').csv("/Users/<USER>/Desktop/playground/resources/start_asset", header=True)

