from pyspark.sql import SparkSession, DataFrame
from pyspark.sql import functions as F
from pyspark.sql.functions import col, count, sum, aggregate, array, lit, row_number, rank, dense_rank, when, from_json, get_json_object, floor, date_format, from_utc_timestamp, round, collect_set
from pyspark.sql.window import Window
from pyspark.sql.types import St<PERSON>ctField, BooleanType, StringType, StructType, DoubleType, LongType, TimestampType, DateType, FloatType, IntegerType, DecimalType, BinaryType, ArrayType, MapType
from pyspark.sql.utils import AnalysisException

spark = SparkSession.builder \
    .appName("Read Snappy Parquet") \
    .getOrCreate()

csv_file_path = "/Users/<USER>/Desktop/playground/resources/csv_read"
df = spark.read.option("recursiveFileLookup", "true").csv(csv_file_path, header=True, inferSchema=True,
                                      multiLine=True)

# df = df.filter(col("account_id") ==  ********)
df.coalesce(1).write.mode('overwrite').csv('/Users/<USER>/Desktop/playground/resources/start_asset', header=True)
