from pyspark.sql import SparkSession, DataFrame
from pyspark.sql import functions as F
from pyspark.sql.functions import col, count, sum, aggregate, array, lit, row_number, rank, dense_rank, when, from_json, get_json_object, floor, date_format, from_utc_timestamp, round, collect_set
from pyspark.sql.window import Window
from pyspark.sql.types import StructField, BooleanType, StringType, StructType, DoubleType, LongType, TimestampType, DateType, FloatType, IntegerType, DecimalType, BinaryType, ArrayType, MapType
from pyspark.sql.utils import AnalysisException
from py4j.java_gateway import java_import

spark = SparkSession.builder \
    .appName("Read Snappy Parquet") \
    .getOrCreate()

old_parquet_file_path = "/Users/<USER>/Downloads/old_snapshot"
niv_old = spark.read.option("recursiveFileLookup", "true").csv(old_parquet_file_path, header=True, inferSchema=True,
                                      multiLine=True)
new_parquet_file_path ="/Users/<USER>/Downloads/new_snapshot"
niv_new = spark.read.option("recursiveFileLookup", "true").csv(new_parquet_file_path, header=True, inferSchema=True,
                                     multiLine=True)
joined_df = niv_old.alias("d1").join(niv_new.alias("d2"), on="account_id", how="inner")
mismatch_df = joined_df.filter(col("d1.invested_value") != col("d2.invested_value"))
distinct_account_ids = mismatch_df.select("account_id", col("d1.invested_value").alias("old_invested_value"), col("d2.invested_value").alias("new_invested_value"), (col("d2.invested_value") - col("d1.invested_value")).alias("difference")).distinct().orderBy(F.abs(col("difference")), ascending=False)

print(f"Total invested amount mismatch count is {distinct_account_ids.count()}")
print(f"Total accountId in old snapshot is {niv_old.select('account_id').distinct().count()}")
print(f"Total accountId in new snapshot is {niv_new.select('account_id').distinct().count()}")
distinct_account_ids.select("account_id", "old_invested_value", "new_invested_value", "difference").coalesce(1).write.mode('overwrite').csv("/Users/<USER>/Desktop/playground/resources/net_niv_mismatch", header=True)

# account_id with fund idr equal to 0
niv_new = niv_new.filter(col("total_fund_invested_idr") == 0)
fund_idr_zero = distinct_account_ids.join(niv_new, on="account_id", how="inner").select("account_id", "old_invested_value", "new_invested_value", "difference").distinct().orderBy(F.abs(col("difference")), ascending=False)

# crypto txns path
crypto_future_txns_path = "/Users/<USER>/Downloads/crypto_txns"
crypto_future_gain = spark.read.option("recursiveFileLookup", "true").parquet(crypto_future_txns_path).withColumn("realised_gain",
                        col("realised_gain") * get_json_object(col("info"), "$.orderUpdateEvent.settleAssetMidPrice").cast("double")).select("account_id", "realised_gain")
crypto_future_gain = crypto_future_gain.groupBy("account_id").agg(sum("realised_gain").alias("realised_gain_value")).filter(col("realised_gain_value") == 0)

# account_id with crypto future gain equal to 0
fund_idr_zero_crypto_gain_zero = fund_idr_zero.join(crypto_future_gain, on="account_id", how="inner").select("account_id", "old_invested_value", "new_invested_value", "difference").distinct().orderBy(F.abs(col("difference")), ascending=False)

fund_idr_zero_crypto_gain_zero.coalesce(1).write.mode('overwrite').csv("/Users/<USER>/Desktop/playground/resources/niv_mismatch", header=True)
print(f"df count is {fund_idr_zero_crypto_gain_zero.count()}")
