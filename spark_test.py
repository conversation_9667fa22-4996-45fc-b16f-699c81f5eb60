from pyspark.sql import SparkSession
from pyspark.sql import functions as F
from pyspark.sql.functions import regexp_extract, when, col

spark = SparkSession.builder \
    .appName("Read Snappy Parquet") \
    .getOrCreate()

parquet_file_path = "/Users/<USER>/Desktop/playground/resources/parquet_read"
df = spark.read.option("recursiveFileLookup", "true").parquet(parquet_file_path)

# Extract data from corrupted JSON columns using regex patterns
staking_assets_df = df.withColumn("reward_frequency",
                                 F.regexp_extract("reward_disbursal_frequency", r'interval["":\s]*([^""]+)', 1)) \
                     .withColumn("reward_cron_schedule",
                                 when(F.regexp_extract("reward_disbursal_frequency", r'cron["":\s]*([^""]+)', 1) != "",
                                      F.regexp_extract("reward_disbursal_frequency", r'cron["":\s]*([^""]+)', 1))
                                 .otherwise(F.regexp_extract("reward_accrual_frequency", r'cron["":\s]*([^""]+)', 1))) \
                     .withColumn("reward_disbursal_window",
                                 F.regexp_extract("reward_disbursal_frequency", r'windowType["":\s]*([^""]+)', 1))

# Select final columns
final_df = staking_assets_df.select("crypto_currency_id", "reward_frequency", "reward_cron_schedule", "reward_disbursal_window")

final_df.show(truncate=False)