# import requests
# import json
# from kafka import KafkaProducer
#
# # Query Pinot
# response = requests.post(
#     "http://localhost:9000/sql",
#     json={"sql": "select * from airlineStats limit 10"},
#     headers={"Content-Type": "application/json"}
# )
#
# print("Pinot response:", response.json())
#
# # Send to Kafka
# producer = KafkaProducer(
#     bootstrap_servers=['localhost:9092'],
#     value_serializer=lambda x: json.dumps(x).encode('utf-8')
# )
#
# producer.send('test-topic', {'test': 'message'})
# producer.flush()
# producer.close()
#
# print("Message sent to Kafka!")

import json
import requests
import time
from kafka import KafkaProducer
from datetime import datetime

# Configuration - Updated with correct broker port
PINOT_BROKER_URL = "http://localhost:9000/sql"  # Changed port
KAFKA_BOOTSTRAP_SERVERS = "localhost:9092"
KAFKA_TOPIC = "pinot-query-results"
PINOT_QUERY = "SELECT * FROM crypto_currency_REALTIME LIMIT 10"

# Initialize Kafka Producer
producer = KafkaProducer(
    bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
    value_serializer=lambda v: json.dumps(v).encode('utf-8')
)


def test_pinot_connection():
    """Test if Pinot broker is accessible"""
    try:
        # Simple ping to check if service is up
        response = requests.get(PINOT_BROKER_URL.replace('/query/sql', '/health'))
        print(f"Pinot health check: {response.status_code}")
    except Exception as e:
        print(f"Pinot service not reachable: {e}")


def query_pinot():
    payload = {
        "sql": PINOT_QUERY
    }

    try:
        print(f"Querying Pinot at: {PINOT_BROKER_URL}")
        print(f"Query: {PINOT_QUERY}")

        response = requests.post(PINOT_BROKER_URL,
                                 json=payload,
                                 headers={'Content-Type': 'application/json'},
                                 timeout=30)

        print(f"Response status: {response.status_code}")

        if response.status_code == 404:
            print("404 Error - Check if:")
            print("1. Pinot broker is running")
            print("2. Correct port (try 8099 instead of 9000)")
            print("3. Correct endpoint path")
            return None

        response.raise_for_status()
        result = response.json()
        print(f"Query successful. Response keys: {result.keys()}")
        return result

    except Exception as e:
        print(f"Error querying Pinot: {e}")
        return None


def send_to_kafka(data):
    if data and 'resultTable' in data:
        message = {
            "timestamp": datetime.now().isoformat(),
            "query_results": data['resultTable'],
            "metadata": {
                "numDocsScanned": data.get('numDocsScanned', 0),
                "totalDocs": data.get('totalDocs', 0),
                "timeUsedMs": data.get('timeUsedMs', 0)
            }
        }

        try:
            producer.send(KAFKA_TOPIC, value=message)
            producer.flush()
            print(f"Sent {len(data['resultTable']['rows'])} rows to Kafka")
        except Exception as e:
            print(f"Error sending to Kafka: {e}")


if __name__ == "__main__":
    print("Testing Pinot connection...")
    test_pinot_connection()

    print("Executing query...")
    result = query_pinot()
    if result:
        send_to_kafka(result)
