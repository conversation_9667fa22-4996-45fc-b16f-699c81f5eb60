import requests

# Test different SQL query endpoints on port 9000
endpoints = [
    "/query/sql",
    "/sql",
    "/query",
    "/broker/query/sql",
    "/pinot-broker/query/sql",
    "/druid/v2/sql"
]

for endpoint in endpoints:
    try:
        response = requests.post(
            f"http://localhost:9000{endpoint}",
            json={"sql": "SHOW TABLES"},
            headers={"Content-Type": "application/json"},
            timeout=2
        )
        print(f"{endpoint}: {response.status_code} - {response.text[:100]}")
    except Exception as e:
        print(f"{endpoint}: Error - {e}")

# Also try GET to see available endpoints
try:
    response = requests.get("http://localhost:9000/", timeout=2)
    print(f"Root endpoint: {response.status_code}")
    if response.status_code == 200:
        print(response.text[:200])
except:
    print("Root endpoint failed")